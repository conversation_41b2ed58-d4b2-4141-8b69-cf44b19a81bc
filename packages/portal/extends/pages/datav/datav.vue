<template>
  <div id="datavContainer" class="datav-container" ref="datavContainer">
    <ADatavHeaderTitle />
    <div class="header-nav">
      <ADatavHeaderNav
        @nav-click="handleNavClick"
        :is-fullscreen="isFullscreen"
      />
    </div>
    <div class="datav-content">
      <div class="datav-content-top">
        <div class="datav-content-left">
          <div class="datav-content-left-item border-left-box-1">
            <a-datav-sub-title title="生产运行情况"></a-datav-sub-title>
            <ADatavProduceStatis :isUnit="true" unit="万元" />
          </div>
          <div class="datav-content-left-item border-left-box-2">
            <a-datav-sub-title title="隐患问题统计图"></a-datav-sub-title>
            <ADatavDailyWordProgress @resize="handleResize" />
          </div>
        </div>
        <div class="datav-content-center">
          <div class="datav-content-center-top-item">
            <div class="datav-content-center-top-item-left border-center-box-1">
              <a-datav-sub-title title="HSE检查情况"></a-datav-sub-title>
              <ADatavHseInspectPlan />
            </div>
            <div
              class="datav-content-center-top-item-right border-center-box-1"
            >
              <a-datav-sub-title title="值班表"></a-datav-sub-title>
              <ADatavDutyTable
                :dutyTableData="dutyTableData"
                @dutyTableClick="handleDutyTableClick"
                @toggleFullScreen="handleToggleFullScreen"
              />
            </div>
          </div>
          <div class="datav-content-center-bottom-item border-center-box-3">
            <div
              style="
                width: 100%;
                height: 100%;
                overflow: hidden;
                padding: 1% 2.5% 2% 2.5%;
              "
            >
              <a-tabs size="small">
                <a-tab-pane key="1" tab="通知公告">
                  <div style="width: 100%; height: 100%; overflow: hidden">
                    <ADatavNoticeList
                      ref="noticeListRef"
                      :list="importantNoticeList"
                      @click="handleNoticeListClick"
                    />
                  </div>
                </a-tab-pane>
                <a-tab-pane key="2" tab="风险提示">
                  <div style="width: 100%; height: 100%; overflow: hidden">
                    <ADatavNoticeList
                      ref="noticeListRef"
                      :list="importantNoticeList"
                      @click="handleNoticeListClick"
                    />
                  </div>
                </a-tab-pane>
                <a-tab-pane key="3" tab="考核通报">
                  <div style="width: 100%; height: 100%; overflow: hidden">
                    <ADatavNoticeList
                      ref="noticeListRef"
                      :list="importantNoticeList"
                      @click="handleNoticeListClick"
                    />
                  </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
        </div>
        <div class="datav-content-right">
          <div class="datav-content-right-item border-right-box-1">
            <a-datav-sub-title title="天气预警"></a-datav-sub-title>
            <ADatavWeatherRisk
              :weatherData="weatherData"
              :weatherAlerts="weatherAlertList"
              @alertClick="handleAlertClick"
              @weekForecastClick="handleShowWeekForecast"
            />
          </div>
          <div class="datav-content-right-item border-right-box-1">
            <a-datav-sub-title title="抢修抢险队伍"></a-datav-sub-title>
            <ADatavBigHiddenDanger
              ref="bigHiddenDangerRef"
              :list="repairTeamList"
              @click="handleRepairTeamListClick"
            />
          </div>
        </div>
      </div>
      <div class="datav-content-bottom">
        <div class="datav-content-bottom-item border-bottom-box-1">
          <a-datav-sub-title title="典型问题曝光台"></a-datav-sub-title>
          <ADatavBannerList
            ref="bannerListRef"
            :list="bannerList"
            @item-click="handleBannerListClick"
          />
        </div>
      </div>
    </div>
    <ADatavModal
      :visible="showWeekForecast"
      title="七天天气预报"
      @cancel="handleCancelWeekForecast"
      width="1000px"
      :showOk="false"
    >
      <ADatavWeather7dList :weather7dData="weather7dData" />
    </ADatavModal>
    <ADatavModal
      :visible="mapStatisVisible"
      title="位置信息"
      @cancel="handleCancelMapStatis"
      width="1000px"
      :showOk="false"
    >
      <div style="width: 100%; height: 600px">
        <ADatavMapStatis :isFullScreen="false" :zoom="6" />
      </div>
    </ADatavModal>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Provide } from 'vue-property-decorator';
import { Tabs } from '@h3/antd-vue';
import * as echarts from 'echarts';
import moment from 'moment';
import * as _ from 'lodash';
import lyApi from '../../ly-api';
import { alarminfo } from '../../utils/data';
import util from '../../utils';

import DatavSafetyDigital from '../../components/datav-safety-digital.vue';
import SubTitle from '../../components/datav/sub-title.vue';
import HeaderNav from '../../components/datav/header-nav.vue';
import WeatherRisk from '../../components/datav/weather-risk.vue';
import DatavExercise from '../../components/datav-exercise.vue';
import DatavModal from '../../components/datav-modal.vue';
import DatavNoticeDetail from '../../components/datav-notice-detail.vue';
import CenerateOperationStatus from '../../components/datav/cenerate-operation-status.vue';
import DailyWordProgress from '../../components/datav/daily-word-progress.vue';
import HseQoQAnalysis from '../../components/datav/hse-qoq-analysis.vue';
import HseInspectPlan from '../../components/datav/hse-inspect-plan.vue';
import NoticeList from '../../components/datav/notice-list.vue';
import WarningList from '../../components/datav/warning-list.vue';
import BigHiddenDanger from '../../components/datav/big-hidden-danger.vue';
import HeaderTitle from '../../components/datav/header-title.vue';
import Weather7dList from '../../components/datav/weather-7d-list.vue';
import ProduceStatis from '../../components/datav/produce-statis.vue';
import HssInspectPlanPie from '../../components/datav/hss-inspect-plan-pie.vue';
import TrainingPlanNavbar from '../../components/datav/training-plan-navbar.vue';
import DutyTable from '../../components/datav/duty-table.vue';
import MapStatis from '../../components/datav/map-statis.vue';
import BannerList from '../../components/datav/banner-list.vue';

@Component({
  name: 'Datav',
  components: {
    ADatavSafetyDigital: DatavSafetyDigital,
    ADatavSubTitle: SubTitle,
    ADatavHeaderNav: HeaderNav,
    ADatavWeatherRisk: WeatherRisk,
    ADatavExercise: DatavExercise,
    ADatavModal: DatavModal,
    ADatavNoticeDetail: DatavNoticeDetail,
    ADatavCenerateOperationStatus: CenerateOperationStatus,
    ADatavDailyWordProgress: DailyWordProgress,
    ADatavHseQoQAnalysis: HseQoQAnalysis,
    ADatavHseInspectPlan: HseInspectPlan,
    ADatavNoticeList: NoticeList,
    ADatavWarningList: WarningList,
    ADatavBigHiddenDanger: BigHiddenDanger,
    ADatavHeaderTitle: HeaderTitle,
    ADatavWeather7dList: Weather7dList,
    ADatavProduceStatis: ProduceStatis,
    ADatavHssInspectPlanPie: HssInspectPlanPie,
    ADatavTrainingPlanNavbar: TrainingPlanNavbar,
    ADatavDutyTable: DutyTable,
    ADatavMapStatis: MapStatis,
    ADatavBannerList: BannerList,
    ATabs: Tabs,
    ATabPane: Tabs.TabPane,
  },
})
export default class Datav extends Vue {
  // 是否全屏
  isFullscreen = false;

  // 窗口大小监听
  resizeObserver: any = null;

  // 重要通知
  importantNoticeList: any[] = [];

  // 政府警示信息
  govWarningInfoList: any[] = [];

  // 墨迹天气信息
  weatherData: any = {};

  // 七天天气预报
  weather7dData: any = {};

  // 天气预警信息
  weatherAlertList: any = [];

  // 重大隐患
  bigHiddenDangerList: any[] = [];

  // 培训计划
  trainingPlanList: any[] = [];

  // 心跳相关属性
  heartbeatTimer: any = null;

  // 心跳间隔
  heartbeatInterval: number = 10000;

  // 七天天气预报弹窗
  showWeekForecast: boolean = false;

  // 图表
  charts: any[] = [];

  // 值班表数据
  dutyTableData: any[] = [];

  // 位置信息弹窗
  mapStatisVisible: boolean = false;

  // 抢修抢险队伍设备及物资统计表
  repairTeamList: any[] = [];

  // 轮播图
  bannerList: any[] = [];

  // 轮播图数据
  noticeTitles: any[] = ['通知公告', '风险提示', '考核通报'];

  // 轮播图索引
  noticeIndex: number = 0;

  /**
   * 通用的数据更新方法
   * @param currentList 当前数据列表
   * @param newList 新数据列表
   * @param compareFields 需要比较的字段数组
   * @returns 更新后的数据列表
   */
  updateDataList(
    currentList: any[],
    newList: any[],
    compareFields: string[] = [],
  ) {
    if (currentList.length === 0) {
      // 如果现有列表为空，直接使用新数据
      return newList;
    }

    // 对比新旧数据，更新变化的数据项
    newList.forEach((newItem) => {
      const existingItemIndex = currentList.findIndex(
        (item) => item.id === newItem.id,
      );
      if (existingItemIndex > -1) {
        // 检查并更新已存在的数据项
        const existingItem = currentList[existingItemIndex];
        compareFields.forEach((field) => {
          if (existingItem[field] !== newItem[field]) {
            this.$set(existingItem, field, newItem[field]);
          }
        });
      } else {
        // 添加新的数据项到列表
        currentList.push(newItem);
      }
    });

    // 移除列表中不再存在的数据项
    for (let i = currentList.length - 1; i >= 0; i--) {
      const item = currentList[i];
      const existsInNewList = newList.some((newItem) => newItem.id === item.id);
      if (!existsInNewList) {
        currentList.splice(i, 1);
      }
    }

    return currentList;
  }

  get _window() {
    return window as any;
  }

  get _document() {
    return this._window.document as any;
  }

  /**
   * 导航栏点击
   * @param index 索引
   */
  handleNavClick(index: number) {
    if (index == 0) {
      this._window.open(
        this.getApplicationUrl('scyxtjb', '2c2c80849758279f019767b325013966'),
        '_blank',
      );
    }
    if (index == 1) {
      this._window.open(
        this.getApplicationUrl('mrsgxctjb', '2c2c80849758279f019767b325013966'),
        '_blank',
      );
    }
    if (index == 4) {
      // 完成网页全屏显示
      this.windowFullscreen();
      this.handleResize(null);
    }
  }

  handleToggleFullScreen(markers: any[]) {
    this.mapStatisVisible = true;
  }

  handleCancelMapStatis() {
    this.mapStatisVisible = false;
  }

  handleAfterChange(current: number) {
    this.noticeIndex = current;
  }

  /**
   * 获取应用URL
   * @param code 应用编码
   * @param parentId 父级ID
   * @returns 应用URL
   */
  getApplicationUrl(code: string, parentId: string) {
    return `${this._window.location.origin}/application/lfzsaq/application-list/${code}?parentId=${parentId}&code=${code}&openMode=&pcUrl=&queryCode=&return=%2Fapp-list%2Fapplications%3Freturn%3D%2Fportal-page%2FdefaultPortalDashboard`;
  }

  /**
   * 窗口全屏
   */
  windowFullscreen() {
    console.log('windowFullscreen');
    this.isFullscreen = !this.isFullscreen;
    const container = this.$refs.datavContainer as any;
    console.log('container', this._document.fullscreenElement);
    if (!this._document.fullscreenElement) {
      // 进入全屏模式，针对容器元素
      if (container.requestFullscreen) {
        container.requestFullscreen();
      } else if (container.webkitRequestFullscreen) {
        container.webkitRequestFullscreen();
      } else if (container.msRequestFullscreen) {
        container.msRequestFullscreen();
      }
    } else {
      // 退出全屏模式
      if (this._document.exitFullscreen) {
        this._document.exitFullscreen();
      } else if (this._document.webkitExitFullscreen) {
        this._document.webkitExitFullscreen();
      } else if (this._document.msExitFullscreen) {
        this._document.msExitFullscreen();
      }
    }
  }

  /**
   * 窗口大小监听
   * @param chart 图表
   */
  handleResize(chart: any) {
    console.log('🚀 ~ Datav ~ handleResize ~ chart:', chart);
    const asideContainer =
      this._window.document.querySelector('.aside-container');
    if (asideContainer) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const newWidth = entry.contentRect.width;
          // 使用$nextTick确保DOM已更新
          this.$nextTick(() => {
            if (chart) {
              chart.resize();
            }
          });
        }
      });

      resizeObserver.observe(asideContainer);
      this.resizeObserver = resizeObserver;
    }
  }

  /**
   * 获取重要通知
   */
  async getImportantNotice() {
    const params = util.$params.getListParams('wjgl');
    const { errcode, data } = await lyApi.getList(params);
    if (errcode == 0) {
      const newList = data.content
        .map((item: any) => {
          return {
            title: item.data.bt,
            time: moment(item.data.rq).format('YYYY-MM-DD'),
            type: item.data.wjlb,
            id: item.id,
          };
        })
        .filter((item: any) => item.title);

      // 使用通用方法更新数据
      this.importantNoticeList = this.updateDataList(
        this.importantNoticeList,
        newList,
        ['title', 'time', 'type'],
      );

      const noticeListComponent = this.$refs.noticeListRef as any;
      if (noticeListComponent) {
        noticeListComponent.$refs.seamlessNoticeScrollRef.reset();
      }

      console.log(
        '🚀 ~ Datav ~ getImportantNotice ~ this.importantNoticeList:',
        data.content,
      );
    }
  }

  /**
   * 通知列表点击
   * @param item 行数据
   */
  async handleNoticeListClick(item: any) {
    if (item) {
      try {
        const res = await lyApi.getFormUrl(
          util.$params.getFormUrlParams(item.id, 'wjgl', 'wjgl'),
        );
        this._window.open(res, '_blank');
      } catch (error) {
        this.$message.error('获取通知详情失败');
      }
    }
  }

  /**
   * 获取政府警示信息
   */
  async getGovWarningInfo() {
    try {
      const res = await lyApi.getGovWarningInfo({});
      // 获取到HTML解析html
      if (res) {
        // 解析html
        const parser = new DOMParser();
        // 解析html
        const doc = parser.parseFromString(res, 'text/html');
        // 获取警示信息
        const warningHtml = doc.body.querySelector('.yingji_warning');
        if (warningHtml) {
          // 获取到所有的警示信息
          const dones = warningHtml.querySelectorAll('.wBox .none');
          this.govWarningInfoList = [];
          dones.forEach((done: any) => {
            const title = done.querySelector('.biref a').textContent;
            const content = done.querySelector('.content a').textContent;
            const href = done.querySelector('.biref a').getAttribute('href');
            const time = href
              ? href.split('/').pop().split('_')[0].replace('t', '')
              : '';
            this.govWarningInfoList.push({
              title,
              content,
              href,
              time: time ? moment(time).format('YYYY年MM月DD日') : '',
            });
          });
        }
      }
    } catch (error) {
      console.log('🚀 ~ Datav ~ getGovWarningInfo ~ error:', error);
    }
  }

  /**
   * 政府警示信息点击
   * @param item 行数据
   */
  handleWarningListClick(item: any) {
    window.open(lyApi.getGovApi() + item.href, '_blank');
  }

  /**
   * 获取墨迹天气信息
   */
  async getCurrentMojiWeatherInfo() {
    try {
      const res = await lyApi.getWeatherMoji({
        cityId: 305,
        cityType: 0,
      });
      console.log('🚀 ~ Datav ~ getCurrentMojiWeatherInfo ~ res:', res);
      if (res) {
        this.weatherData = res;
        console.log(
          '🚀 ~ Datav ~ getCurrentMojiWeatherInfo ~ this.weatherData:',
          this.weatherData,
        );
      }
    } catch (error) {
      console.log('🚀 ~ Datav ~ getCurrentMojiWeatherInfo ~ error:', error);
    }
  }

  handleShowWeekForecast(data: any) {
    this.weather7dData = this.weatherData;
    this.showWeekForecast = true;
  }

  handleCancelWeekForecast() {
    this.showWeekForecast = false;
  }

  /**
   * 获取天气预警信息
   */
  async getAlarmInfo() {
    try {
      const res = await lyApi.getWeatherAlarm({});
      const data = JSON.parse(
        res.replace('var alarminfo=', '').replace(';', ''),
      );
      console.log('🚀 ~ Datav ~ getAlarmInfo ~ data:', data.data);

      this.weatherAlertList = data.data
        .filter((item: any) => item[0].indexOf('黑龙江省大庆市') > -1)
        .map((item: any) => {
          const fileNamePart = item[1];
          const codeMatch = fileNamePart.match(/\d{4}\.html$/);
          const code = codeMatch ? codeMatch[0].replace('.html', '') : '';

          // 拆分前两位数字
          const typeCode = code.substring(0, 2);
          const levelCode = code.substring(2, 4);

          return {
            title:
              item[0] +
              util.$alarminfo.alarminfo.kind[typeCode] +
              util.$alarminfo.alarminfo.grade[levelCode] +
              '预警',
            time: moment(item[4].split('_')[1].substring(0, 8)).format(
              'YYYY-MM-DD',
            ),
            grade: util.$alarminfo.alarminfo.grade[levelCode],
            kind: util.$alarminfo.alarminfo.kind[typeCode],
            href: item[1],
            longitude: item[2],
            latitude: item[3],
            typeCode: typeCode,
            levelCode: levelCode,
          };
        });
    } catch (error) {
      console.log('🚀 ~ Datav ~ getAlarmInfo ~ error:', error);
    }
  }

  /**
   * 天气预警点击
   * @param alert 行数据
   */
  handleAlertClick(alert: any) {
    window.open(lyApi.getAlarmApi() + alert.href, '_blank');
  }

  /***
   * 获取训计划
   */
  async getTrainingPlan() {
    const { errcode, data } = await lyApi.getList(
      util.$params.getListParams('pxjh'),
    );
    if (errcode == 0) {
      this.trainingPlanList = data.content.map((item: any) => {
        return {
          title: item.data.tbdw,
          time: item.data.createdTime,
          id: item.id,
          child: item.data.Sheet1749090605413,
        };
      });
    }
  }

  /**
   * 重大隐患点击
   * @param item 行数据
   */
  async handleTrainingPlanClick(item: any) {
    if (item) {
      const res = await lyApi.getFormUrl({
        bizObjectId: item.id,
        schemaCode: 'pxjh',
        formCode: 'pxjh',
      });
      this._window.open(res, '_blank');
    }
  }

  /**
   * 获取值班表数据
   */
  async getDutyTableData() {
    const { errcode, data } = await lyApi.getList(
      util.$params.getListParams('zbb'),
    );
    if (errcode == 0) {
      const dates = [];
      data.content.map((item: any) => {
        if (item.data.Sheet1750665922811) {
          item.data.Sheet1750665922811.map((child: any) => {
            child.zb = item.data.zb;
            child.zbrq1 = moment(child.zbrq1).format('YYYY-MM-DD');
            dates.push(child);
          });
        }
      });
      console.log('🚀 ~ Datav ~ getDutyTableData ~ dates:', dates);
      if (dates.length > 0) {
        const date = moment().format('YYYY-MM-DD');
        console.log('🚀 ~ Datav ~ getDutyTableData ~ date:', date);
        const dateData = dates.filter((item: any) => item.zbrq1 == date);
        if (dateData.length > 0) {
          this.dutyTableData = dateData.length > 1 ? dateData[0] : dateData;
        }
      }
      console.log(
        '🚀 ~ Datav ~ getDutyTableData ~ this.dutyTableData:',
        this.dutyTableData,
      );
    }
  }

  async handleDutyTableClick(item: any) {
    console.log('🚀 ~ Datav ~ handleDutyTableClick ~ item:', item);
    if (item) {
      const res = await lyApi.getFormUrl(
        util.$params.getFormUrlParams(item.parentId, 'zbb', 'zbb'),
      );
      this._window.open(res, '_blank');
    }
  }

  /**
   * 获取HSE检查计划
   */
  async getHSECheckPlan() {
    try {
      const { errcode, data } = await lyApi.getServiceTestApi(
        util.$params.getServiceTestParams('weekCount', 'lfzsaq'),
      );
      console.log('🚀 ~ Datav ~ getHSECheckPlan ~ data:', data);

      console.log('🚀 ~ Datav ~ getHSECheckPlan ~ errcode:', errcode);
    } catch (error) {}
  }

  async handleRepairTeamListClick(item: any) {
    console.log('🚀 ~ Datav ~ handleRepairTeamListClick ~ item:', item);
    if (item) {
      const res = await lyApi.getFormUrl(
        util.$params.getFormUrlParams(item.id, 'yjdw', 'yjdw'),
      );
      this._window.open(res, '_blank');
    }
  }

  /**
   * 获取抢修抢险队伍设备及物资统计表
   */
  async getRepairTeamList() {
    const { errcode, data } = await lyApi.getList(
      util.$params.getListParams('yjdw'),
    );
    if (errcode == 0) {
      const newList = data.content.map((item: any) => {
        return {
          dw:
            item.data.dw && item.data.dw.length > 0
              ? item.data.dw[0].name
              : '无',
          dwsd: item.data.dwsd,
          id: item.id,
          rs: item.data.rs,
          time: item.data.createdTime,
        };
      });

      // 使用通用方法更新数据
      this.repairTeamList = this.updateDataList(this.repairTeamList, newList, [
        'dw',
        'dwsd',
        'rs',
        'time',
      ]);

      const bigHiddenDangerComponent = this.$refs.bigHiddenDangerRef as any;
      if (bigHiddenDangerComponent) {
        bigHiddenDangerComponent.$refs.seamlessBigHiddenDangerScrollRef.reset();
      }
    }
  }

  async handleBannerListClick(item: any) {
    if (item) {
      const res = await lyApi.getFormUrl(
        util.$params.getFormUrlParams(item.id, 'lbt', 'lbt'),
      );
      this._window.open(res, '_blank');
    }
  }

  async getBannerList() {
    const { errcode, data } = await lyApi.getList(
      util.$params.getListParams('lbt', {
        queryCondition: [
          {
            queryFilterType: 'Eq',
            propertyCode: 'zt',
            propertyType: 12,
            propertyValue: '[{"key":"option1","value":"启用"}]',
          },
        ],
      }),
    );
    if (errcode == 0) {
      const newList = data.content.map((item: any) => {
        return {
          id: item.id,
          img:
            item.data.Attachment1753413268350 &&
            item.data.Attachment1753413268350.length > 0
              ? lyApi.getDownloadFile({
                  refId: item.data.Attachment1753413268350[0].refId,
                })
              : '',
          title: item.data.bt,
          time: item.data.createdTime,
          isLoading: true,
        };
      });

      // 使用通用方法更新数据
      this.bannerList = this.updateDataList(this.bannerList, newList, [
        'img',
        'title',
        'time',
        'isLoading',
      ]);

      // 使用 $nextTick 确保 DOM 更新后再调用 reset
      this.$nextTick(() => {
        const bannerListComponent = this.$refs.bannerListRef as any;
        if (
          bannerListComponent &&
          bannerListComponent.$refs &&
          bannerListComponent.$refs.seamlessScrollRef
        ) {
          console.log(
            '🚀 ~ Datav ~ getBannerList ~ bannerListComponent:',
            bannerListComponent,
          );
          bannerListComponent.$refs.seamlessScrollRef.reset();
          // bannerListComponent.$refs.seamlessScrollRef._startMove();
        }
      });
    }
  }

  /**
   * 启动心跳
   * @param interval 时间间隔(毫秒)
   */
  startHeartbeat(interval?: number) {
    // 清除已有定时器
    this.clearHeartbeat();

    // 更新间隔时间(如果提供)
    if (interval && interval > 0) {
      this.heartbeatInterval = interval;
    }

    // 立即执行一次
    this.executeHeartbeat();

    // 设置定时器
    this.heartbeatTimer = setInterval(() => {
      this.executeHeartbeat();
    }, this.heartbeatInterval);
  }

  /**
   * 执行心跳操作
   */
  executeHeartbeat() {
    console.log('执行心跳，当前时间:', new Date().toLocaleString());
    // 在这里添加需要定时执行的操作
    // 例如：刷新数据、检查连接状态等
    this.getImportantNotice();
    this.getCurrentMojiWeatherInfo();
    this.getGovWarningInfo();
    this.getAlarmInfo();
    this.getRepairTeamList();
    this.getHSECheckPlan();
    this.getDutyTableData();
    this.getBannerList();
  }

  /**
   * 清除心跳
   */
  clearHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // 大屏自适应
  BigScreen(id, options) {
    // 获取dom元素
    let { width, height } = options;
    let app = document.querySelector(id); // 最好使用id 选择器

    // 设置缩放的位置
    app.style.transformOrigin = 'top left';

    function init() {
      // 获取元素与视口宽高的比例
      let scaleX = innerWidth / width;
      let scaleY = innerHeight / height;
      // 设置缩放比例（取最小值）
      let scale = Math.min(scaleX, scaleY);

      let translateX = (innerWidth - width * scale) / 2;
      let translateY = (innerHeight - height * scale) / 2;

      // 5.设置平移的距离和缩放比例
      app.style.transform = `translate(${translateX}px,${translateY}px) scale(${scale})`;
    }

    init();
    window.addEventListener('resize', () => {
      init();
    });
  }

  mounted() {
    this.$nextTick(() => {
      this.handleResize(null);
      // 启动心跳，会自动执行所有数据获取方法
      this.startHeartbeat();
      this.BigScreen('#datavContainer', {
        width: document.getElementById('datavContainer').offsetWidth,
        height: document.getElementById('datavContainer').offsetHeight,
      });
    });
  }

  beforeDestroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    this.clearHeartbeat();
  }
}
</script>

<style scoped lang="less">
.datav-container-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.datav-container {
  width: 100%;
  height: 100%;
  background: #00065b url(../../assets/images/datav-bg.jpg) no-repeat top center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  transition: all 0.3s ease;

  .header-nav {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0 84px;
    font-size: 14px;
    color: #fff;
    margin-top: -2%;
  }

  .datav-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 0 2.5%;
    height: 87.5%;
    margin-top: 0.5%;
    gap: 0.5%;

    /deep/.ant-tabs {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    /deep/.ant-tabs-nav .ant-tabs-tab {
      color: #fff;
    }

    &-top {
      width: 100%;
      height: 66.66%;
      display: flex;
      gap: 0.5%;
    }

    &-bottom {
      width: 100%;
      height: 33.33%;
      display: flex;

      &-item {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        border: 2px solid #2e6099;
        border-radius: 10px;
        box-sizing: border-box;
      }
    }

    &-left {
      width: 30%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &-item {
        width: 100%;
        height: 49.5%;
        display: flex;
        flex-direction: column;
        position: relative;
        border: 2px solid #2e6099;
        border-radius: 10px;
        box-sizing: border-box;
      }
      // &-item.border-left-box-1 {
      //   display: flex;
      //   background: url(../../assets/images/datav-border-left-bg-2.png)
      //     no-repeat;
      //   background-size: 100% 100%;
      //   background-repeat: no-repeat;

      // }

      // &-item.border-left-box-2,
      // &-hse-item.border-left-box-2 {
      //   display: flex;
      //   background: url(../../assets/images/datav-border-left-bg-2.png)
      //     no-repeat center;
      //   background-size: 100% 100%;
      //   background-repeat: no-repeat;
      // }

      // &-item.border-left-box-3,
      // &-hse-item.border-left-box-3 {
      //   display: flex;
      //   background: url(../../assets/images/datav-border-left-bg-3.png)
      //     no-repeat center;
      //   background-size: 100% 100%;
      //   background-repeat: no-repeat;
      // }

      &-hse-item {
        width: 100%;
        height: calc(33.33% - 0.33%);
        display: flex;
        flex-direction: column;
        position: relative;
      }

      &-progress-item,
      &-check-item {
        width: 100%;
        height: calc(33.33% - 0.33%);
        display: flex;
      }

      .chart-container {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
      }
    }

    &-center {
      width: 40%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &-top-item {
        width: 100%;
        height: 49.5%;
        display: flex;
        gap: 1%;

        &-left {
          width: 50%;
          height: 100%;
          display: flex;
          flex-direction: column;
          position: relative;
          border: 2px solid #2e6099;
          border-radius: 10px;
          box-sizing: border-box;
        }
        // &-left.border-center-box-1 {
        //   display: flex;
        //   background: url(../../assets/images/datav-border-center-bg-1.png)
        //     no-repeat;
        //   background-size: 100% 100%;
        //   background-repeat: no-repeat;
        // }
        &-right {
          width: 50%;
          height: 100%;
          display: flex;
          flex-direction: column;
          position: relative;
          border: 2px solid #2e6099;
          border-radius: 10px;
          box-sizing: border-box;
        }
        // &-right.border-center-box-1 {
        //   display: flex;
        //   background: url(../../assets/images/datav-border-center-bg-1.png)
        //     no-repeat;
        //   background-size: 100% 100%;
        //   background-repeat: no-repeat;
        // }
      }
      &-bottom-item {
        width: 100%;
        height: 49.5%;
        display: flex;
        flex-direction: column;
        position: relative;
        border: 2px solid #2e6099;
        border-radius: 10px;
        box-sizing: border-box;
      }
      // &-bottom-item.border-center-box-3 {
      //   display: flex;
      //   background: url(../../assets/images/datav-border-center-bg-3.png)
      //     no-repeat center;
      //   background-size: 100% 100%;
      //   background-repeat: no-repeat;
      // }
    }

    &-right {
      width: 30%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &-item {
        width: 100%;
        height: 49.5%;
        display: flex;
        flex-direction: column;
        position: relative;
        border: 2px solid #2e6099;
        border-radius: 10px;
        box-sizing: border-box;
      }
      // &-item.border-right-box-1 {
      //   display: flex;
      //   background: url(../../assets/images/datav-border-right-bg-1.png)
      //     no-repeat;
      //   background-size: 100% 100%;
      //   background-repeat: no-repeat;
      // }

      &-exercise-item {
        width: 100%;
        height: 49.5%;
        display: flex;
      }
    }
  }
}
</style>
