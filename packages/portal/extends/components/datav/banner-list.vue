<template>
  <div class="banner-list-container">
    <vue-seamless-scroll
      class="banner-list-seamless-scroll"
      :data="list"
      :class-option="options"
    >
      <div class="banner-list-item-container">
        <div
          class="banner-list-item"
          v-for="item in list"
          :key="item.id"
          @click="handleItemClick(item)"
        >
          <div class="banner-image-container">
            <a-spin
              :spinning="item.isLoading"
              style="
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10;
              "
            >
            </a-spin>
            <img
              @load="handleImageLoad(item)"
              :src="item.img"
              :style="{
                opacity: item.isLoading ? 0 : 1,
                transition: 'opacity 0.3s',
              }"
            />
            <span class="banner-title">{{ item.title }}</span>
          </div>
          <span class="banner-overlay">
            <span class="banner-detail">点击查看详情</span>
          </span>
        </div>
      </div>
    </vue-seamless-scroll>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import vueSeamlessScroll from 'vue-seamless-scroll';

@Component({
  name: 'BannerList',
  components: {
    vueSeamlessScroll,
  },
})
export default class BannerList extends Vue {
  @Prop({ type: Array, default: () => [] }) list!: any[];

  options = {
    limitMoveNum: 4,
    direction: 3,
    step: 1,
    singleWidth: 320,
    waitTime: 2000,
    isWaitTime: true,
  };

  handleItemClick(item: any) {
    this.$emit('item-click', item);
  }

  handleImageLoad(item: any) {
    // 图片加载完成后，关闭spin
    this.$set(item, 'isLoading', false);
  }

  mounted() {
    // 确保每个数据项都有isLoading属性，默认设置为true
    if (this.list && Array.isArray(this.list)) {
      this.list.forEach((item) => {
        if (
          item &&
          typeof item === 'object' &&
          !item.hasOwnProperty('isLoading')
        ) {
          this.$set(item, 'isLoading', true);
        }
      });
    }
  }
}
</script>

<style lang="less" scoped>
.banner-list-container {
  width: 100%;
  height: 100%;
  padding: 2% 1% 1% 1%;
  box-sizing: border-box;
  overflow: hidden;

  .banner-list-seamless-scroll {
    width: 100%;
    height: 200px;
    overflow: hidden;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0;
    // 使用clip-path创建斜角效果
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );

    /deep/[bis_skin_checked='1'] {
      height: 100%;
    }

    /deep/.ant-spin {
      height: auto;
    }
  }
  .banner-list-item-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .banner-list-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    border-radius: 10px;
    position: relative;
    cursor: pointer;
    width: 300px;
    height: 100%;

    .banner-image-container {
      position: relative;
      width: 300px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 10px;
      }

      .banner-title {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.4);
        color: white;
        padding: 6px 10px;
        font-size: 14px;
        text-align: left;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .banner-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .banner-detail {
        color: white;
        font-size: 12px;
        font-weight: bold;
      }
    }

    &:hover {
      .banner-overlay {
        opacity: 1;
      }
    }
  }
}
</style>
