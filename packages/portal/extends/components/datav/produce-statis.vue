<template>
  <div class="produce-statis">
    <div class="produce-statis-content">
      <!-- 左侧 - 生产数据 -->
      <div class="data-section">
        <div class="section-title">生产</div>
        <div class="data-items">
          <div class="data-item" style="--border-color: #ff5a6e">
            <div class="data-info">
              <div class="indicator-label">产品</div>
              <div class="data-value">
                <span class="number" style="color: #ff5a6e">232</span>
              </div>
            </div>
            <div class="data-unit" style="color: #ff5a6e">
              万元
            </div>
          </div>

          <div class="data-item" style="--border-color: #ffb55d">
            <div class="data-info">
              <div class="indicator-label">基建</div>
              <div class="data-value">
                <span class="number" style="color: #ffb55d">232</span>
              </div>
            </div>
            <div class="data-unit" style="color: #ffb55d">
              万元
            </div>
          </div>

          <div class="data-item" style="--border-color: #04d6c8">
            <div class="data-info">
              <div class="indicator-label">井下</div>
              <div class="data-value">
                <span class="number" style="color: #04d6c8">232</span>
              </div>
            </div>
            <div class="data-unit" style="color: #04d6c8">
              万元
            </div>
          </div>

          <div class="data-item" style="--border-color: #0b8aff">
            <div class="data-info">
              <div class="indicator-label">天然气</div>
              <div class="data-value">
                <span class="number" style="color: #0b8aff">232</span>
              </div>
            </div>
            <div class="data-unit" style="color: #0b8aff">
              万元
            </div>
          </div>
        </div>
      </div>
      <div class="data-line"></div>
      <!-- 右侧 - 施工数据 -->
      <div class="data-section">
        <div class="section-title">施工</div>
        <div class="data-items">
          <div class="data-item" style="--border-color: #ff5a6e">
            <div class="data-info">
              <div class="indicator-label">产品</div>
              <div class="data-value">
                <span class="number" style="color: #ff5a6e">80</span>
              </div>
            </div>
            <!-- <div class="data-icon">
              <div class="cube cube-red"></div>
            </div> -->
            <div class="data-unit" style="color: #ff5a6e">
              个
            </div>
          </div>

          <div class="data-item" style="--border-color: #ffb55d">
            <div class="data-info">
              <div class="indicator-label">基建</div>
              <div class="data-value">
                <span class="number" style="color: #ffb55d">90</span>
              </div>
            </div>
            <!-- <div class="data-icon">
              <div class="cube cube-gold"></div>
            </div> -->
            <div class="data-unit" style="color: #ffb55d">
              个
            </div>
          </div>

          <div class="data-item" style="--border-color: #04d6c8">
            <div class="data-info">
              <div class="indicator-label">井下</div>
              <div class="data-value">
                <span class="number" style="color: #04d6c8">100</span>
              </div>
            </div>
            <!-- <div class="data-icon">
              <div class="cube cube-teal"></div>
            </div> -->
            <div class="data-unit" style="color: #04d6c8">
              个
            </div>
          </div>

          <div class="data-item" style="--border-color: #0b8aff">
            <div class="data-info">
              <div class="indicator-label">天然气</div>
              <div class="data-value">
                <span class="number" style="color: #0b8aff">110</span>
              </div>
            </div>
            <!-- <div class="data-icon">
              <div class="cube cube-blue"></div>
            </div> -->
            <div class="data-unit" style="color: #0b8aff">
              个
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
@Component({
  name: 'ProduceStatis',
})
export default class ProduceStatis extends Vue {
  @Prop({ type: Array, default: () => [] }) produceData!: any[];
}
</script>

<style scoped lang="less">
.produce-statis {
  width: 100%;
  height: 100%;
  padding: 6.5% 3% 2% 3%;
  box-sizing: border-box;
  position: relative;

  @font-face {
    font-family: 'Digital-7';
    src: url('../../assets/fonts/DS-DIGIT.TTF') format('truetype');
    font-weight: normal;
    font-style: normal;
  }
  .data-line {
    width: 1px;
    height: 100%;
    background-color: #01cad9;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 100;
    transform: translate(-50%, -50%) scale(0.8);
  }
  .produce-statis-content {
    width: 100%;
    height: 100%;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0;
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );
    display: flex;
    justify-content: space-between;
    gap: 2%;

    .data-unit {
      font-size: 14px;
      font-weight: bold;
      height: 100%;
      display: flex;
      align-items: end;
      padding: 10px 0px;
      box-sizing: border-box;
    }

    .data-section {
      width: 50%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .section-title {
        font-size: 14px;
        font-weight: bold;
        color: #01cad9;
        text-align: center;
        padding: 6px 0;
        border-bottom: 1px solid rgba(1, 202, 217, 0.3);
        position: relative;

        &:after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 40%;
          width: 20%;
          height: 2px;
          background: linear-gradient(
            to right,
            transparent,
            #01cad9,
            transparent
          );
        }
      }

      .data-items {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        justify-content: space-between;
        align-items: center;
        height: 100%;
        gap: 4%;
        padding: 4% 0;
        padding-left: 2%;
        padding-right: 2%;
        box-sizing: border-box;

        .data-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1.5% 10%;
          background: linear-gradient(
            to right,
            rgba(0, 0, 0, 0.3),
            rgba(20, 20, 30, 0.15)
          );
          border-radius: 4px;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
          transition: all 0.3s ease;
          width: 100%; /* 设置宽度为48%，确保一行放两个 */
          height: 100%;
          box-sizing: border-box;
          position: relative;
          border-left: 3px solid var(--border-color);

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
            
            .data-icon .cube {
              transform: rotateX(45deg) rotateZ(45deg) scale(1.2);
            }
          }

          .data-info {
            flex: 1;

            .indicator-label {
              color: rgba(255, 255, 255, 0.6);
              font-size: 14px;
              margin-bottom: 5px;
            }

            .data-value {
              .number {
                font-size: 22px;
                font-weight: bold;
                color: #ffffff;
                font-family: 'Digital-7', sans-serif;
                letter-spacing: 1px;
              }
            }

            .data-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .subtitle {
                color: rgba(255, 255, 255, 0.7);
                font-size: 14px;
              }

              .trend {
                display: flex;
                align-items: center;

                &.up {
                  color: #04d6c8;
                }

                &.down {
                  color: #ff5a6e;
                }

                .trend-arrow {
                  font-style: normal;
                  font-size: 12px;
                  margin-right: 2px;
                }

                .trend-value {
                  font-size: 14px;
                }
              }
            }
          }

          .data-icon {
            margin-left: 10px;

            .cube {
              width: 20px;
              height: 20px;
              position: relative;
              transform: rotateX(45deg) rotateZ(45deg);
              transform-style: preserve-3d;
              transition: transform 0.3s ease;
              box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

              &:before,
              &:after {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                transition: all 0.3s ease;
              }

              &:before {
                transform: rotateX(90deg);
                transform-origin: bottom;
                bottom: 100%;
              }

              &:after {
                transform: rotateY(-90deg);
                transform-origin: right;
                right: 100%;
              }

              &.cube-red {
                background: rgba(255, 90, 110, 0.8);
                &:before {
                  background: rgba(255, 90, 110, 0.4);
                }
                &:after {
                  background: rgba(255, 90, 110, 0.6);
                }
                &:hover {
                  background: rgba(255, 90, 110, 1);
                }
              }

              &.cube-gold {
                background: rgba(255, 181, 93, 0.8);
                &:before {
                  background: rgba(255, 181, 93, 0.4);
                }
                &:after {
                  background: rgba(255, 181, 93, 0.6);
                }
                &:hover {
                  background: rgba(255, 181, 93, 1);
                }
              }

              &.cube-teal {
                background: rgba(4, 214, 200, 0.8);
                &:before {
                  background: rgba(4, 214, 200, 0.4);
                }
                &:after {
                  background: rgba(4, 214, 200, 0.6);
                }
                &:hover {
                  background: rgba(4, 214, 200, 1);
                }
              }

              &.cube-blue {
                background: rgba(11, 138, 255, 0.8);
                &:before {
                  background: rgba(11, 138, 255, 0.4);
                }
                &:after {
                  background: rgba(11, 138, 255, 0.6);
                }
                &:hover {
                  background: rgba(11, 138, 255, 1);
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
