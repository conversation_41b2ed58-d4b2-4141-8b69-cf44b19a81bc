<template>
  <div class="duty-table">
    <div class="duty-table-panel">
      <!-- 值班表 - 卡片式布局 -->
      <div class="duty-table-card">
        <div class="duty-table-card-content" v-if="dutyTableData && dutyTableData.length > 0" @click="handleDutyTableClick">
          <div class="duty-info-row">
            <div class="duty-info-value-date">{{ dutyTableData[0].zbrq1 }}</div>
          </div>
          <div class="duty-info-row">
            <div class="duty-info-label">值班领导:</div>
            <div class="duty-info-value highlight">
              {{ dutyTableData[0].zbld }}
            </div>
          </div>
          <div class="duty-info-row duty-member-row">
            <div class="duty-info-label">值班干部:</div>
            <div class="duty-info-value">
              <div class="duty-members-container">
                <div class="duty-member">
                  {{ dutyTableData[0]?.zbgb }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="duty-table-card-empty" v-else>暂无值班信息</div>
      </div>

      <!-- 分隔线 --> 
      <!-- <div class="divider-line">
        <span class="divider-line-dot"></span>
      </div>

      <div class="map-statis-container">
        <div class="btns-container">
          <div class="btn-item" v-for="(btn, index) in btns" :key="index" @click="handleBtnClick(btn)">
            <a-icon :type="btn.icon" />
            <span>{{ btn.name }}</span>
          </div>
        </div>
        <div class="map-statis-content">
          <MapStatis @toggleFullScreen="handleToggleFullScreen" />
        </div>
      </div> -->

      <!-- 抢修抢险队伍 
      <div class="duty-table-demand-team">
        <div
          class="duty-table-header"
          style="display: flex; justify-content: center"
        >
          <div>抢修抢险队伍</div>
        </div>
        <div class="duty-table-demand-team-content">
          <div class="duty-table-demand-team-item">
            <div
              v-for="(team, index) in emergencyTeams"
              :key="index"
              class="team-row"
            >
              <div class="team-row-left">
                <div class="team-name">{{ team.name || '--' }}</div>
                <div class="team-contact">{{ team.contact || '--' }}</div>
              </div>
              <div class="team-row-right">
                <div>{{ team.duty || '--' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>-->
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import MapStatis from './map-statis.vue';
@Component({
  name: 'DutyTable',
  components: {
    MapStatis,
  },
})
export default class DutyTable extends Vue {
  @Prop({ type: Array, default: () => [] }) dutyTableData!: any[];
  @Prop({
    type: Array,
    default: () => [
      {
        name: '毕喜柱',
        contact: '13804690111',
        duty: '经理助理',
      },
      {
        name: '刘士海',
        contact: '13904866003',
        duty: '监督管理中心主任',
      },
      {
        name: '刘艳庆',
        contact: '15846185270',
        duty: '生产安全部副主任',
      },
      {
        name: '金林',
        contact: '13339470613',
        duty: '生产安全部干事',
      },
      {
        name: '张明',
        contact: '13104090008',
        duty: '生产指挥中心副主任',
      },
    ],
  })
  emergencyTeams!: any[];

  btns = [
    {
      name: '应急预案',
      icon: 'icon-zhibanbiao',
    },
    {
      name: '应急物资',
      icon: 'icon-zhibanbiao',
    },
    {
      name: '应急队伍',
      icon: 'icon-zhibanbiao',
    },
    {
      name: '应急设备',
      icon: 'icon-zhibanbiao',
    },
  ];

  handleDutyTableClick() {
    this.$emit('dutyTableClick', this.dutyTableData[0]);
  }

  handleBtnClick(btn: any) {
    this.$emit('btnClick', btn);
  }

  handleToggleFullScreen(markers: any[]) {
    this.$emit('toggleFullScreen', markers);
  }
}
</script>

<style scoped lang="less">
.duty-table {
  width: 100%;
  height: 100%;
  padding: 10% 3% 2% 3%;
  box-sizing: border-box;

  &-panel {
    width: 100%;
    height: 100%;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0px;
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  &-card,
  &-demand-team {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &-card {
    height: 100%;  
    cursor: pointer;

    &-content {
      flex: 1;
      padding: 10px 15px;
      display: flex;
      flex-direction: column;
    }

    &-empty {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #e8f6ff;
      font-size: 14px;
    }
  }

  &-demand-team {
    height: 62%;  
  }

  .map-statis-container {
    width: 100%;
    height: 62%;
    position: relative;

    .map-statis-content {
      width: 100%;
      height: 84%;
    }

    .btns-container {
      height: 16%;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
    }

    .btn-item {
      display: flex;
      align-items: center;
      backdrop-filter: blur(4px);
      border-bottom: 1px solid rgba(0, 219, 255, 0.3);
      border-radius: 4px;
      padding: 6px 10px;
      font-size: 12px;
      color: #e8f6ff;
      cursor: pointer;
      transition: all 0.25s ease;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
      max-width: 100px;

      &:hover {
        background: rgba(0, 139, 219, 0.8);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      &:active {
        transform: translateY(0px);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
      }

      i {
        font-size: 14px;
        margin-right: 4px;
      }

      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  &-header {
    display: flex;
    background: linear-gradient(
      90deg,
      rgba(0, 102, 204, 0.7),
      rgba(0, 181, 233, 0.5)
    );
    color: #ffffff;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2px 0;
    position: relative;
    font-size: 12px;

    border-radius: 0px;
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      100% 10px,
      100% 100%,
      0 100%,
      0 10px
    );
  }

  .duty-info-row {
    display: flex;
    padding: 15px 10px;
    color: #e8f6ff;
    position: relative;
    align-items: center;
    line-height: 20px;
      border-bottom: 1px dashed rgba(1, 202, 217, 0.3);

    &:first-child {
      padding-top: 6px;
    }

    &.duty-member-row {
      align-items: flex-start;
    }

    .duty-info-label {
      width: 80px;
      font-weight: 500;
      color: rgba(232, 246, 255, 0.8);
    }

    .duty-info-value {
      flex: 1;
      font-size: 18px;
      font-weight: 500;

      &.highlight {
        color: #faad14;
        font-weight: 500;
        font-size: 26px;
      }
    }

    .duty-info-value-date {
      width: 100%;
      font-size: 26px;
      font-weight: bold;
      color: #00dbff;
      text-align: center;
      padding: 10px 0;
    }

    .duty-members-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .duty-member {
      font-size: 14px;
      font-weight: 500;
    }
  }

  &-demand-team {
    &-content {
      flex: 1;
      overflow-y: auto;

      .team-row {
        display: flex;
        padding: 13px 0;
        border-bottom: 1px solid rgba(0, 219, 255, 0.1);
        color: #e8f6ff;
        font-size: 14px;

        &:nth-child(odd) {
          background-color: rgba(1, 202, 217, 0.05);
        }

        &:nth-child(even) {
          background-color: rgba(1, 202, 217, 0.1);
        }

        &:hover {
          background-color: rgba(0, 219, 255, 0.1);
        }

        &-left {
          width: 60%;
          display: flex;
          justify-content: space-between;

          .team-name,
          .team-contact {
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .team-name {
            width: 40%;
          }

          .team-contact {
            width: 60%;
          }
        }

        &-right {
          width: 40%;
          text-align: center;

          span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0 5px;
          }
        }

        span {
          flex: 1;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding: 0 5px;
        }
      }
    }
  }
}
</style>
